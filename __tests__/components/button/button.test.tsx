import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Button } from "../../../src/components/common";

describe("Button Component", () => {
  it("should render button correctly", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText("Click me")).toBeInTheDocument();
  });

  it("should apply correct classes for different variants", () => {
    const variants: NonNullable<ButtonProps["variant"]>[] = [
      "primary",
      "secondary",
      "accent",
      "outline",
      "ghost",
      "link",
      "icon",
    ];

    variants.forEach((variant) => {
      const { container, unmount } = render(
        <Button variant={variant}>Click me</Button>
      );
      const button = container.firstChild as HTMLElement;

      expect(button).toHaveClass(VARIANT_CLASSES[variant]);

      unmount();
    });
  });
});
