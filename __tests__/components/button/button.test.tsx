import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Button } from "../../../src/components/common";

// Define ButtonProps type for testing
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  variant?:
    | "primary"
    | "secondary"
    | "accent"
    | "outline"
    | "ghost"
    | "link"
    | "icon";
}

// Define variant classes for testing
const VARIANT_CLASSES: Record<NonNullable<ButtonProps["variant"]>, string> = {
  accent: "border-none btn-accent text-base-content hover:bg-accent/60",
  ghost:
    "border-none bg-transparent hover:bg-transparent hover:border-none boreder-none shadow-none",
  icon: "btn-icon border-none h-7 w-7 !rounded-lg p-0",
  link: "btn-link hover:text-primary-content shadow-none",
  outline:
    "btn-outline border hover:bg-secondary-content hover:border-secondary-content shadow-none",
  primary: "border-none btn-primary text-base-100 hover:bg-primary/70",
  secondary: "border-none btn-secondary text-base-100 hover:bg-secondary/70",
};

describe("Button Component", () => {
  it("should render button correctly", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText("Click me")).toBeInTheDocument();
  });

  it("should apply correct classes for different variants", () => {
    const variants: NonNullable<ButtonProps["variant"]>[] = [
      "primary",
      "secondary",
      "accent",
      "outline",
      "ghost",
      "link",
      "icon",
    ];

    variants.forEach((variant) => {
      const { container, unmount } = render(
        <Button variant={variant}>Click me</Button>
      );
      const button = container.firstChild as HTMLElement;

      // Check that variant-specific classes are applied
      const variantClasses = VARIANT_CLASSES[variant].split(" ");
      variantClasses.forEach((className) => {
        expect(button).toHaveClass(className);
      });

      unmount();
    });
  });

  it("should apply custom className", () => {
    const { container } = render(
      <Button className="custom-class">Click me</Button>
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Button>Click me</Button>);
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn", "shadow-xs", "hover:shadow-none", "h-9");
  });

  it("should apply primary as default variant", () => {
    const { container } = render(<Button>Click me</Button>);
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn-primary");
  });

  it("should handle click events", () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Clickable Button</Button>);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("should pass through HTML button attributes", () => {
    render(
      <Button disabled id="test-button" data-testid="custom-button">
        Disabled Button
      </Button>
    );

    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute("id", "test-button");
    expect(button).toHaveAttribute("data-testid", "custom-button");
  });

  it("should have correct button type by default", () => {
    render(<Button>Type Button</Button>);
    const button = screen.getByRole("button");

    expect(button).toHaveAttribute("type", "button");
  });

  it("should render different content types", () => {
    const { rerender } = render(<Button>Text Content</Button>);
    expect(screen.getByText("Text Content")).toBeInTheDocument();

    rerender(
      <Button>
        <span>Nested Content</span>
      </Button>
    );
    expect(screen.getByText("Nested Content")).toBeInTheDocument();

    rerender(
      <Button>
        <div>Complex</div>
        <span>Content</span>
      </Button>
    );
    expect(screen.getByText("Complex")).toBeInTheDocument();
    expect(screen.getByText("Content")).toBeInTheDocument();
  });

  it("should handle icon variant correctly", () => {
    const { container } = render(
      <Button variant="icon">
        <i className="icon-test" />
      </Button>
    );
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn-icon");
    expect(button).toHaveClass("h-7");
    expect(button).toHaveClass("w-7");
    expect(button).toHaveClass("!rounded-lg");
    expect(button).toHaveClass("p-0");
  });

  it("should combine custom className with variant classes", () => {
    const { container } = render(
      <Button variant="outline" className="my-custom-class">
        Combined Classes
      </Button>
    );
    const button = container.firstChild as HTMLElement;

    // Should have custom class
    expect(button).toHaveClass("my-custom-class");
    // Should have variant classes
    expect(button).toHaveClass("btn-outline");
    expect(button).toHaveClass("border");
    // Should have base classes
    expect(button).toHaveClass("btn");
  });

  it("should render as button element", () => {
    render(<Button>Button Element</Button>);
    const button = screen.getByRole("button");

    expect(button.tagName).toBe("BUTTON");
  });
});
