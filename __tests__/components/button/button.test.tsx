import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Button } from "../../../src/components/common";

describe("Button Component", () => {
  it("should render button correctly", () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText("Click me")).toBeInTheDocument();
  });

  it("should apply correct classes for different variants", () => {
    const variants = [
      "primary",
      "secondary",
      "accent",
      "outline",
      "ghost",
      "link",
      "icon",
    ] as const;

    variants.forEach((variant) => {
      const { container, unmount } = render(
        <Button variant={variant}>Click me</Button>
      );
      const button = container.firstChild as HTMLElement;

      switch (variant) {
        case "primary":
          expect(button).toHaveClass("btn-primary");
          break;
        case "secondary":
          expect(button).toHaveClass("btn-secondary");
          break;
        case "accent":
          expect(button).toHaveClass("btn-accent");
          break;
        case "outline":
          expect(button).toHaveClass("btn-outline");
          break;
        case "ghost":
          expect(button).toHaveClass("bg-transparent");
          break;
        case "link":
          expect(button).toHaveClass("btn-link");
          break;
        case "icon":
          expect(button).toHaveClass("btn-icon");
          expect(button).toHaveClass("h-7");
          expect(button).toHaveClass("w-7");
          break;
      }

      unmount();
    });
  });

  it("should apply custom className", () => {
    const { container } = render(
      <Button className="custom-class">Click me</Button>
    );

    expect(container.firstChild).toHaveClass("custom-class");
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Button>Click me</Button>);
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn", "shadow-xs", "hover:shadow-none", "h-9");
  });

  it("should apply primary as default variant", () => {
    const { container } = render(<Button>Click me</Button>);
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn-primary");
  });

  it("should handle click events", () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Clickable Button</Button>);

    const button = screen.getByRole("button");
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it("should pass through HTML button attributes", () => {
    render(
      <Button disabled id="test-button" data-testid="custom-button">
        Disabled Button
      </Button>
    );

    const button = screen.getByRole("button");
    expect(button).toBeDisabled();
    expect(button).toHaveAttribute("id", "test-button");
    expect(button).toHaveAttribute("data-testid", "custom-button");
  });

  it("should have correct button type by default", () => {
    render(<Button>Type Button</Button>);
    const button = screen.getByRole("button");

    expect(button).toHaveAttribute("type", "button");
  });

  it("should render different content types", () => {
    const { rerender } = render(<Button>Text Content</Button>);
    expect(screen.getByText("Text Content")).toBeInTheDocument();

    rerender(
      <Button>
        <span>Nested Content</span>
      </Button>
    );
    expect(screen.getByText("Nested Content")).toBeInTheDocument();

    rerender(
      <Button>
        <div>Complex</div>
        <span>Content</span>
      </Button>
    );
    expect(screen.getByText("Complex")).toBeInTheDocument();
    expect(screen.getByText("Content")).toBeInTheDocument();
  });

  it("should handle icon variant correctly", () => {
    const { container } = render(
      <Button variant="icon">
        <i className="icon-test" />
      </Button>
    );
    const button = container.firstChild as HTMLElement;

    expect(button).toHaveClass("btn-icon");
    expect(button).toHaveClass("h-7");
    expect(button).toHaveClass("w-7");
    expect(button).toHaveClass("!rounded-lg");
    expect(button).toHaveClass("p-0");
  });

  it("should combine custom className with variant classes", () => {
    const { container } = render(
      <Button variant="outline" className="my-custom-class">
        Combined Classes
      </Button>
    );
    const button = container.firstChild as HTMLElement;

    // Should have custom class
    expect(button).toHaveClass("my-custom-class");
    // Should have variant classes
    expect(button).toHaveClass("btn-outline");
    expect(button).toHaveClass("border");
    // Should have base classes
    expect(button).toHaveClass("btn");
  });

  it("should render as button element", () => {
    render(<Button>Button Element</Button>);
    const button = screen.getByRole("button");

    expect(button.tagName).toBe("BUTTON");
  });
});
