import { render, screen } from "@testing-library/react";
import { describe, it, expect } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Textarea } from "../../../src/components/common";

describe("Textarea Component", () => {
  it("should render textarea correctly", () => {
    render(<Textarea placeholder="Type here" />);

    expect(screen.getByPlaceholderText("Type here")).toBeInTheDocument();
    expect(screen.getByRole("textbox")).toBeInTheDocument();
  });

  it("should apply base classes correctly", () => {
    const { container } = render(<Textarea placeholder="Type here" />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass(
      "textarea",
      "textarea-primary",
      "h-full",
      "rounded-lg",
      "border-none",
      "bg-base-200",
      "text-body-xs",
      "placeholder:text-color-neutral"
    );
  });

  it("should apply custom className", () => {
    const { container } = render(<Textarea className="custom-class" />);
    const textarea = container.firstChild as HTMLElement;

    expect(textarea).toHaveClass("custom-class");
  });
});
