import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Select } from "../../../src/components/common";

describe("Select Component", () => {
  it("should render select correctly", () => {
    render(<Select />);
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("should display options when clicked", () => {
    render(<Select />);
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(screen.getAllByRole("button")).toHaveLength(3);
  });

  it("should close options when clicked outside", () => {
    render(<Select />);
    const button = screen.getByRole("button");
    fireEvent.click(button);
    expect(screen.getAllByRole("button")).toHaveLength(3);
    fireEvent.click(document);
    expect(screen.queryAllByRole("button")).toHaveLength(1);
  });

  //   it("should call onChange when an option is selected", () => {
  //     const onChange = vi.fn();
  //     render(<Select onChange={onChange} />);
  //     const options = screen.getAllByRole("button");
  //     fireEvent.click(options[1]);
  //     expect(onChange).toHaveBeenCalledWith("No");
  //   });
});
