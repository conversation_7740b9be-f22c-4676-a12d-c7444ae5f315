import { render, screen, fireEvent } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";
import "@testing-library/jest-dom/vitest";
import { Input } from "../../../src/components/common";

describe("Input Component", () => {
  it("should render input correctly", () => {
    render(<Input placeholder="Test Input" />);
    expect(screen.getByPlaceholderText("Test Input")).toBeInTheDocument();
  });

  it("shoud apply base classes correctly", () => {
    const { container } = render(<Input placeholder="Test Input" />);
    const input = container.firstChild as HTMLElement;
    expect(input).toHaveClass("hover:bg-base-200 [&.active]:bg-base-200");
  });

  it("should apply correct classes for different variants", () => {
    const variants = ["default", "transparent"] as const;

    variants.forEach((variant) => {
      const { container, unmount } = render(
        <Input variant={variant} placeholder="Test Input" />
      );
      const input = container.firstChild as HTMLElement;

      switch (variant) {
        case "default":
          expect(input).toHaveClass("bg-base-200");
          break;
        case "transparent":
          expect(input).toHaveClass("bg-transparent");
          break;
      }

      unmount();
    });
  });

  it("should handle click events when isButton is true", () => {
    const handleClick = vi.fn();
    render(<Input isButton onClick={handleClick} />);

    const input = screen.getByRole("button");
    fireEvent.click(input);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
